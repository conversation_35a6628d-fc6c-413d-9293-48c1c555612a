<template>
  <div class="page-container">
    <game-title @game-info-update="handleGameInfoUpdate" @plan-update="handlePlanUpdate" />
    <div class="today-public-opinion">
      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl1" alt="今日舆情图标" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>今日舆情</h3>
            <p>{{ publicOpinionData.publicOpinion }}个</p>
          </div>
        </div>
      </div>
      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl2" alt="参与讨论人数图标" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>参与讨论人数</h3>
            <p>{{ publicOpinionData.participantsNumber }}个</p>
          </div>
        </div>
      </div>
      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl3" alt="正面舆情图标" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>正面舆情</h3>
            <p>{{ publicOpinionData.sumPositive }}个</p>
          </div>
          <a-button type="link" class="ellipsis-button" @click="goToPositiveOpinionPage">
            <img :src="moreIcon" alt="更多" class="more-icon" />
          </a-button>
        </div>
      </div>

      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl4" alt="负面舆情图标" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>负面舆情</h3>
            <p>{{ publicOpinionData.sumNegative }}个</p>
          </div>
          <a-button type="link" class="ellipsis-button" @click="goToNegativeOpinionPage">
            <img :src="moreIcon" alt="更多" class="more-icon" />
          </a-button>
        </div>
      </div>
    </div>

    <div class="main-content">
      <div class="left-column" ref="leftColumnRef">
        <div class="opinion-source">
          <div class="header-with-more">
            <h2>舆情渠道</h2>
            </div>
          <nightingale-pie-chart :chart-data="sourceList" width="100%" height="350px" />
        </div>
        <div class="hot-news">
          <div class="header-with-more">
            <h2>今日热点</h2>
            </div>
          <div class="hot-layout">
            <div class="hot-table-container">
              <table class="hot-table">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>名称</th>
                    <th>热度</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in newsList" :key="item.token || index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.token }}</td>
                    <td>{{ item.tokenCount }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="word-cloud-container">
              <word-cloud-chart :chart-data="newsChartList" />
            </div>
          </div>
        </div>

        <div class="hot-topic">
          <div class="header-with-more">
            <h2>今日热门话题</h2>
            </div>
          <div class="hot-layout">
            <div class="hot-table-container">
              <table class="hot-table">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>话题</th>
                    <th>负面占比</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in newsList_topic" :key="item.token || index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.token }}</td>
                    <td>{{ item.negativeProportion }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="word-cloud-container">
              <word-cloud-chart :chart-data="newsChartList_topic" />
            </div>
          </div>
        </div>
      </div>

      <div class="right-column" ref="rightColumnRef">
        <div class="header-with-more">
          <div class="title-container">
            <h2>热帖</h2>
            <div v-if="showRecentPostsTip" class="recent-posts-tip">
              <div class="tip-content">
                <Icon icon="ant-design:info-circle-outlined" class="tip-icon" />
                <span class="tip-text">今日无热帖，为您展示最近热帖</span>
              </div>
            </div>
          </div>
          <div class="scroll-indicator" v-if="hasMore">
            <Icon icon="ant-design:arrow-down-outlined" class="scroll-icon" />
            <span>向下滚动查看更多</span>
          </div>
        </div>
        <div class="hot-posts-list" ref="hotPostsListRef" @scroll="handleScroll">
          <div class="hot-post-item" v-for="(post, index) in hotPostsData" :key="post.id">
            <span class="post-rank">{{ index + 1 }}</span>
            <div class="post-content">
              <a :href="post.postUrl" target="_blank" class="post-title-link">
                <span class="post-title">{{ post.title }}</span>
              </a>
              <span class="post-date">{{ post.date }}</span>
            </div>
            <div class="post-views-container">
              <img :src="retieIcon" alt="热帖图标" class="post-views-icon" />
              <span class="post-views">{{ post.views }}</span>
            </div>
          </div>
          <div class="loading-container" v-if="loadingMore">
            <div class="loading-more">
              <img :src="loadingLogo" alt="加载中" class="loading-logo-img" />
              <span>加载中...</span>
            </div>
          </div>
          <div class="no-more-data" v-if="!hasMore && hotPostsData.length > 0">
            <span>没有更多数据了</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import { useGameStore } from '/@/store/modules/gameStore';
import { Icon } from '/@/components/Icon';
import logoUrl1 from '@/assets/images/jinri.png';
import logoUrl2 from '@/assets/images/taolun.png';
import logoUrl3 from '@/assets/images/py.png';
import logoUrl4 from '@/assets/images/ny.png';
import retieIcon from '@/assets/images/retie.png';
import moreIcon from '@/assets/images/moreee.png';
import loadingLogo from '@/assets/images/logo.png';
import gameTitle from '../components/gameTitle.vue';
import NightingalePieChart from '../components/chart/NightingalePieChart.vue';
import WordCloudChart from '../components/chart/WordCloudChart.vue'; // 确保正确导入
import {
  getPublicOpinionDataSourceApi,
  getPublicOpinionDataTodayApi,
  getHotTodayDataApi,
  getHotPostListApi,
  getTrendingTodayApi,
} from '@/api/public-opinion-monitoring/today';
import { usePlanStore } from '@/store/modules/planStore';

const router = useRouter();
const userStore = useUserStore();
const gameStore = useGameStore();
const planStore = usePlanStore();

// 选中游戏Id (从 store 获取)
const gameId = computed(() => gameStore.gameInfo.gameId);
// 获取用户Id
const userId = userStore.getUserInfo?.id;
// 选中planId (从 store 获取)
const selectedPlanId = computed(() => planStore.selectedPlanId);

// 舆情渠道数据
const sourceList = ref([]);

// 获取舆情渠道-饼图数据
const fetchPieData = async (currentPlanId) => {
  if (!currentPlanId || currentPlanId === '') {
    console.warn('fetchPieData: planId为空，跳过请求。');
    return;
  }
  try {
    const localDate = new Date().getTime();
    const params = { planId: currentPlanId, userId, localDate };
    const res = await getPublicOpinionDataSourceApi(params);
    if (res && res.source) {
      sourceList.value = res.source;
    } else {
      console.warn('fetchPieData: 接口返回数据不符合预期或为空', res);
      sourceList.value = [];
    }
  } catch (error) {
    console.error('获取舆情渠道饼图数据异常：', error);
    sourceList.value = [];
  }
};

// 跳转到正面舆情页面
const goToPositiveOpinionPage = () => {
  const currentPlanId = localStorage.getItem('currentPlanId') || '';
  router.push({ name: 'positive-detail', query: { planId: currentPlanId } });
};

// 跳转到负面舆情页面
const goToNegativeOpinionPage = () => {
  const currentPlanId = localStorage.getItem('currentPlanId') || '';
  router.push({ name: 'negative-detail', query: { planId: currentPlanId } });
};

// 热点新闻数据
const newsList = ref([]);
const newsChartList = ref([]);

// 热门话题数据
const newsList_topic = ref([]);
const newsChartList_topic = ref([]);

// 热帖数据相关状态
const hotPostsData = ref([]);
const currentPage = ref(1);
const hasMore = ref(true);
const loadingMore = ref(false);
const lastPostTime = ref(''); // 这个变量在您现在的逻辑中似乎没有被使用，可以考虑移除或启用

// 添加ref引用
const leftColumnRef = ref(null);
const rightColumnRef = ref(null);
const hotPostsListRef = ref(null);

// 动态调整右侧热帖窗口高度以匹配左侧内容
const adjustRightColumnHeight = () => {
  if (leftColumnRef.value && rightColumnRef.value && hotPostsListRef.value) {
    const leftColumnHeight = leftColumnRef.value.offsetHeight;
    const rightColumnPadding = 40; // 上下padding各20px
    const headerHeight = 60; // 标题区域大约60px

    const hotPostsListHeight = leftColumnHeight - rightColumnPadding - headerHeight - 14;
    const minHeight = 400;
    const finalHeight = Math.max(hotPostsListHeight, minHeight);

    hotPostsListRef.value.style.height = `${finalHeight}px`;
    // console.log('调整右侧热帖列表高度至:', finalHeight);
  }
};

// 监听窗口大小变化
const handleResize = () => {
  setTimeout(() => {
    adjustRightColumnHeight();
  }, 100);
};

// 监听 planStore 中 selectedPlanId 的变化来触发数据加载
watch(
  selectedPlanId,
  async (newPlanId) => {
    console.log('watch selectedPlanId触发，新planId:', newPlanId);
    if (newPlanId) {
      // 确保 gameId 已经可用，或者在 handlePlanUpdate 中处理 gameId 为空的情况
      await handlePlanUpdate({ planId: newPlanId, gameId: gameId.value });
    } else {
      // 当没有选中方案时，清空数据
      clearAllData();
      localStorage.removeItem('currentPlanId');
    }
  },
  { immediate: true } // 立即执行一次，确保初始加载
);

// 监听 gameStore 中 gameInfo.gameId 的变化 (虽然目前没有直接触发所有数据重载，但可以在需要时添加)
watch(
  gameId,
  (newGameId, oldGameId) => {
    if (newGameId !== oldGameId && oldGameId !== undefined) { // 避免初始加载时重复清空
      console.log('watch gameId触发，游戏切换：', oldGameId, '->', newGameId);
      // 游戏切换时，如果 planId 不变，也需要重新加载数据 (取决于业务逻辑)
      // 但更推荐由 selectedPlanId 变化来驱动数据的全面加载
      // 这里的清空是为了确保如果 gameId 变化但 selectedPlanId 没有变化时也能清空数据
      // clearAllData(); // 根据实际需求，这里是否需要清空可以再斟酌
    }
  }
);


onMounted(() => {
  window.addEventListener('resize', handleResize);
  // Initial height adjustment
  nextTick(() => {
    adjustRightColumnHeight();
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 获取热帖数据
const fetchHotPostData = async (currentPlanId, isLoadMore = false, pagesToLoad = 1) => {
  if (!currentPlanId || currentPlanId === '') {
    console.warn('fetchHotPostData: planId为空，跳过请求。');
    return;
  }

  if (!isLoadMore) {
    console.log('--- 清空热帖数据并重置分页 ---');
    hotPostsData.value = [];
    currentPage.value = 1;
    lastPostTime.value = '';
    hasMore.value = true;
  }

  if (!hasMore.value || loadingMore.value) {
    console.log('fetchHotPostData: 没有更多数据或正在加载，跳过请求。');
    return;
  }

  try {
    loadingMore.value = true;
    console.log(`fetchHotPostData: 开始获取热帖数据，方案ID：${currentPlanId}，请求页数：${pagesToLoad}，当前页码：${currentPage.value}`);
    
    if (!userId) {
      console.error('fetchHotPostData: 用户ID不存在');
      return;
    }

    let allFormattedData = [];
    const startPage = currentPage.value;
    const localDate = new Date().getTime();

    for (let i = 0; i < pagesToLoad; i++) {
      const pageToLoad = startPage + i;
      const params = {
        planId: currentPlanId, // 使用传递进来的 currentPlanId
        userId,
        page: pageToLoad,
        localDate,
      };

      console.log(`fetchHotPostData: 请求第 ${pageToLoad} 页参数：`, params);
      const res = await getHotPostListApi(params);
      console.log(`fetchHotPostData: 第 ${pageToLoad} 页接口返回：`, res);

      if (!res) {
        console.warn(`fetchHotPostData: 第 ${pageToLoad} 页接口返回数据为空。`);
        break;
      }

      let resultsData = [];
      if (res.code === 200 && res.result?.results) {
        resultsData = res.result.results;
      } else if (res.results && Array.isArray(res.results)) {
        resultsData = res.results;
      } else {
        console.warn(`fetchHotPostData: 接口返回数据格式无法解析（第 ${pageToLoad} 页）：`, res);
        break;
      }

      if (resultsData.length === 0) {
        hasMore.value = false;
        console.log('fetchHotPostData: 没有更多数据了。');
        break;
      }

      const formattedData = resultsData.map((item) => {
        let title = item.title;
        if (title === '无' || !title) {
          title = item.content.length > 30 ? item.content.substring(0, 30) + '...' : item.content;
        }
        title = title.replace(/\r?\n/g, ' ');

        let formattedDate = '暂无日期';
        if (item.postDate) {
          try {
            const timestamp = parseInt(item.postDate);
            if (!isNaN(timestamp)) {
              const date = new Date(timestamp);
              formattedDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
            }
          } catch (e) {
            console.error('日期格式化错误:', e);
          }
        }
        return {
          id: item.id || Math.random().toString(36).substring(2, 11), // 优先使用后端ID，否则使用随机ID
          title: title,
          date: formattedDate,
          views: item.amount ? item.amount.toString() : '0', // 确保 views 存在并为字符串
          postUrl: item.postUrl || '#',
        };
      });
      allFormattedData = [...allFormattedData, ...formattedData];
    }

    if (isLoadMore) {
      hotPostsData.value = [...hotPostsData.value, ...allFormattedData];
    } else {
      hotPostsData.value = allFormattedData;
    }
    
    // 只有当有数据加载进来时才更新页码
    if (allFormattedData.length > 0) {
      currentPage.value = startPage + pagesToLoad;
    }

    console.log('fetchHotPostData: 当前热帖数据量：', hotPostsData.value.length);
  } catch (error) {
    console.error('获取热帖数据异常：', error);
  } finally {
    loadingMore.value = false;
    nextTick(() => { // 确保DOM更新后调整高度
      adjustRightColumnHeight();
    });
  }
};

// 处理滚动事件
const handleScroll = async (e) => {
  const { scrollTop, scrollHeight, clientHeight } = e.target;
  if (scrollHeight - scrollTop - clientHeight < 100 && !loadingMore.value && hasMore.value) {
    const currentPlanId = localStorage.getItem('currentPlanId');
    if (!currentPlanId) {
      console.warn('handleScroll: 未找到planId，无法加载更多。');
      return;
    }
    console.log('handleScroll: 触发加载更多热帖...');
    await fetchHotPostData(currentPlanId, true, 1);
  }
};

// 获取今日舆情数据
const fetchPublicOpinionData = async (currentPlanId) => {
  if (!currentPlanId || currentPlanId === '') {
    console.warn('fetchPublicOpinionData: planId为空，跳过请求。');
    return;
  }
  try {
    if (!userId) {
      console.error('fetchPublicOpinionData: 用户ID不存在');
      return;
    }
    const localDate = new Date().getTime();
    const params = { planId: currentPlanId, userId, localDate };
    // console.log('今日舆情数据请求参数：', params);
    const res = await getPublicOpinionDataTodayApi(params);
    // console.log('今日舆情数据接口返回：', res);
    if (res && res.results) {
      publicOpinionData.value = res.results;
    } else {
      console.warn('fetchPublicOpinionData: 接口返回数据不符合预期或为空', res);
      // 保持清空状态
      publicOpinionData.value = { publicOpinion: 0, publicOpinionToday: 0, participantsNumber: 0, sumPositive: 0, sumNegative: 0, };
    }
  } catch (error) {
    console.error('获取今日舆情数据异常：', error);
    publicOpinionData.value = { publicOpinion: 0, publicOpinionToday: 0, participantsNumber: 0, sumPositive: 0, sumNegative: 0, };
  }
};

// 获取今日热点数据
const fetchHotTodayData = async (currentPlanId) => {
  if (!currentPlanId || currentPlanId === '') {
    console.warn('fetchHotTodayData: planId为空，跳过请求。');
    return;
  }
  const localDate = new Date().getTime();
  try {
    const params = { planId: currentPlanId, userId, localDate };
    const res = await getHotTodayDataApi(params);
    if (res && res.results) {
      // 确保 item 具有 token 和 tokenCount 字段
      newsList.value = res.results.slice(0, 5).map((item) => ({
        token: item.token,
        tokenCount: item.tokenCount,
      }));
      newsChartList.value = res.results.slice(0, 30).map((item) => ({
        title: item.token,
        views: item.tokenCount.toString(),
      }));
    } else {
      console.warn('fetchHotTodayData: 接口返回数据不符合预期或为空', res);
      newsList.value = [];
      newsChartList.value = [];
    }
  } catch (error) {
    console.error('获取今日热点数据异常：', error);
    newsList.value = [];
    newsChartList.value = [];
  }
};

// 获取今日热门话题数据
const fetchTrendingTodayData = async (currentPlanId) => {
  if (!currentPlanId || currentPlanId === '') {
    console.warn('fetchTrendingTodayData: planId为空，跳过请求。');
    return;
  }
  const localDate = new Date().getTime();
  try {
    const params = { planId: currentPlanId, userId, localDate };
    const res = await getTrendingTodayApi(params);

    if (!res) {
      console.warn('fetchTrendingTodayData: 接口返回数据为空');
      newsList_topic.value = [];
      newsChartList_topic.value = [];
      return;
    }

    let resultsData = [];
    if (res.code === 200 && res.result?.results) {
      resultsData = res.result.results;
    } else if (res.results && Array.isArray(res.results)) {
      resultsData = res.results;
    } else {
      console.warn('fetchTrendingTodayData: 接口返回数据格式无法解析：', res);
      newsList_topic.value = [];
      newsChartList_topic.value = [];
      return;
    }

    newsList_topic.value = resultsData.slice(0, 5).map((item) => ({
      token: item.token,
      negativeProportion: item.negativeProportion, // 确保字段名正确
    }));
    newsChartList_topic.value = resultsData.slice(0, 30).map((item) => ({
      title: item.token,
      views: item.negativeProportion, // 确保字段名正确
    }));
  } catch (error) {
    console.error('获取今日热门话题数据异常：', error);
    newsList_topic.value = [];
    newsChartList_topic.value = [];
  }
};

// 清空所有数据的方法
const clearAllData = () => {
  console.log('--- 执行 clearAllData ---');
  publicOpinionData.value = {
    publicOpinion: 0,
    publicOpinionToday: 0,
    participantsNumber: 0,
    sumPositive: 0,
    sumNegative: 0,
  };
  sourceList.value = [];
  newsList.value = [];
  newsChartList.value = [];
  newsList_topic.value = [];
  newsChartList_topic.value = [];
  hotPostsData.value = [];
  currentPage.value = 1;
  hasMore.value = true;
  lastPostTime.value = '';
};

// 处理游戏信息更新 (通常在选择游戏后触发)
const handleGameInfoUpdate = (gameData) => {
  // 当游戏信息更新时，理论上会随之更新 selectedPlanId，
  // 此时 selectedPlanId 的 watch 会触发 handlePlanUpdate 来加载数据。
  // 所以这里可以只清空数据，等待 selectedPlanId 的watch来驱动后续加载。
  console.log('handleGameInfoUpdate触发，清空所有数据。');
  clearAllData();
  localStorage.removeItem('currentPlanId'); // 游戏切换时也清空planId缓存
};

// 处理方案更新 (在 handleGameInfoUpdate 之后，或者直接选择方案时触发)
const handlePlanUpdate = async (planData) => {
  console.log('handlePlanUpdate触发，planData:', planData);
  if (planData.planId && planData.planId !== '') {
    localStorage.setItem('currentPlanId', planData.planId);
    console.log('开始获取所有数据...');
    // 清空数据，确保不会出现旧数据残留
    clearAllData(); 
    
    // 并行请求数据，提高效率
    await Promise.all([
      fetchPublicOpinionData(planData.planId),
      fetchPieData(planData.planId),
      fetchHotTodayData(planData.planId),
      fetchTrendingTodayData(planData.planId),
      fetchHotPostData(planData.planId, false, 2), // 初始加载前两页热帖
    ]);
    console.log('所有数据加载完成。');

    // 等待DOM更新后调整高度
    await nextTick();
    setTimeout(() => {
      adjustRightColumnHeight();
    }, 200); // 增加一点延迟，确保所有DOM都渲染完毕
  } else {
    console.log('handlePlanUpdate: planId为空，清空所有数据。');
    clearAllData();
    localStorage.removeItem('currentPlanId');
  }
};

// 舆情数据
const publicOpinionData = ref({
  publicOpinion: 0,
  publicOpinionToday: 0,
  participantsNumber: 0,
  sumPositive: 0,
  sumNegative: 0,
});

// 查看更多方法
const viewMore = (section) => {
  console.log(`查看更多 ${section}`);
};

// 计算属性，用于控制“今日无热帖”提示的显示
const showRecentPostsTip = computed(() => {
  // 当 hotPostsData 为空且没有加载中状态时显示提示
  return hotPostsData.value.length === 0 && !loadingMore.value;
});
</script>

<style scoped>
/* 页面整体布局 */
.page-container {
  font-family: Arial, sans-serif;
  width: 100%;
  padding: 20px;
  background-color: #f9f9f9;
}

/* 顶部外框 */
.public-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 顶部栏 */
.top-bar {
  display: flex;
  align-items: center;
  width: 100%;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  width: 70px;
  height: 70px;
  border-radius: 10px;
}

.game-details {
  display: flex;
  flex-direction: column;
}

.game-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.game-subtitle {
  font-size: 14px;
  color: gray;
  margin: 0;
}

/* 竖线样式 */
.vertical-line {
  width: 1px;
  height: 80px;
  background-color: #ccc;
  margin-left: 50px;
  margin-right: 50px;
}

/* 搜索栏 */
.search-bar {
  flex: 1;
  width: 500px;
  margin: 0 20px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 10px;
  color: #888;
  font-size: 16px;
}

.search-input {
  padding: 8px 8px 8px 40px;
  border: 1px solid #ccc;
  border-radius: 50px;
  width: 100%;
  font-size: 16px;
  background-color: #f1f3f5;
}

/* 方案栏 */
.prog-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
}

.select-plan {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
  width: 200px;
}

/* 配置监控方案按钮样式 */
.configure-button {
  color: #018ffb; /* 蓝色 */
  font-size: 14px;
  padding: 0; /* 去掉默认内边距，保持文字按钮风格 */
  background: transparent; /* 透明背景 */
  border: none; /* 去掉边框 */
  cursor: pointer;
  margin-left: 10px; /* 与选择框保持一定距离 */
}

.configure-button:hover {
  color: #0170c9; /* 悬停时颜色变深 */
  text-decoration: underline; /* 悬停时添加下划线 */
}

/* 舆情数据 */
.today-public-opinion {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.today-opinion-item {
  flex: 1;
  margin: 0 10px;
}

.today-opinion-box {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.today-opinion-image {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  margin-right: 15px;
}

.today-opinion-text {
  text-align: left;
  flex: 1;
}

.today-opinion-text h3 {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
}

.today-opinion-text p {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.today-opinion-box h3,
.today-opinion-box p {
  margin: 0;
}

.more-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.ellipsis-button:hover .more-icon {
  transform: scale(1.2);
}

/* 左右两列布局 */
.main-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start; /* 改为顶部对齐，不强制拉伸 */
}

.left-column {
  flex: 2; /* 左半边占 2/3 宽度 */
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-column {
  flex: 1; /* 右半边占 1/3 宽度 */
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  /* 让右侧容器高度自然适应左侧内容高度 */
  height: fit-content;
}

.header-with-more {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* 标题样式 */
.opinion-source h2,
.hot-news h2,
.hot-topic h2,
.right-column h2 {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.opinion-source h2 {
  border-bottom: 4px solid #018ffb;
  width: 80px;
}

.hot-news h2 {
  border-bottom: 4px solid #018ffb;
  width: 80px;
}

.hot-topic h2 {
  border-bottom: 4px solid #018ffb;
  width: 120px;
}

.right-column h2 {
  border-left: 4px solid #018ffb;
  padding-left: 10px;
  height: 30px;
  margin-bottom: 15px;
  font-size: 22px;
  display: flex;
  align-items: center;
}

/* 查看更多按钮样式 */
.more-button {
  color: #018ffb; /* 蓝色，与页面其他蓝色元素一致 */
  font-size: 14px;
  padding: 0; /* 去掉默认内边距，保持文字按钮风格 */
  background: transparent; /* 透明背景 */
  border: none; /* 去掉边框 */
  cursor: pointer;
}

.more-button:hover {
  color: #0170c9; /* 悬停时颜色变深，与 view-more 按钮一致 */
  text-decoration: underline; /* 悬停时添加下划线 */
}

/* 滚动指示器样式 */
.scroll-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #018ffb;
  animation: pulse 2s infinite;
}

.scroll-icon {
  margin-right: 5px;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 热帖列表样式 */
.hot-posts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
  padding-right: 5px;
  margin-top: 10px;
  /* 高度将通过JavaScript动态设置 */
  position: relative; /* 添加相对定位，使加载容器能够正确定位 */
}

/* 自定义滚动条样式 */
.hot-posts-list::-webkit-scrollbar {
  width: 6px;
}

.hot-posts-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.hot-posts-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.hot-posts-list::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.hot-post-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 8px;
  border-bottom: 1px solid #eee;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin-bottom: 2px;
}

.hot-post-item:hover {
  background-color: #f0f7ff;
  transform: translateX(3px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.hot-post-item:nth-child(-n + 3) {
  background-color: #f9f9f9;
}

.post-rank {
  font-size: 18px;
  font-weight: bold;
  color: #018ffb; /* 编号为蓝色 */
  width: 30px; /* 固定宽度，确保对齐 */
  text-align: center;
  background-color: #f0f7ff;
  border-radius: 50%;
  height: 30px;
  line-height: 30px;
  display: inline-block;
}

.hot-post-item:nth-child(-n + 3) .post-rank {
  color: #fff;
  background-color: #e74c3c;
}

.post-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px; /* 标题和日期之间的间距 */
}

.post-title-link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  display: block;
}

.post-title {
  font-size: 16px;
  color: #333; /* 标题为黑色 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  display: block;
  transition: color 0.2s ease;
}

.hot-post-item:hover .post-title {
  color: #018ffb;
}

.hot-post-item:nth-child(-n + 3) .post-title {
  font-weight: 600;
}

.post-date {
  font-size: 14px;
  color: #666; /* 日期为灰色 */
}

.post-views-container {
  display: flex;
  align-items: center;
  background-color: #fff5f5;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 5px;
}

.post-views-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.post-views {
  font-size: 14px;
  color: #e74c3c; /* 浏览量为红色 */
  min-width: 40px; /* 固定宽度，确保对齐 */
  text-align: right;
  font-weight: 500;
}

/* 舆情渠道 */
.opinion-source {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#sourceChart {
  width: 100%;
  height: 400px;
}

/* 热点和话题布局 */
.hot-news,
.hot-topic {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hot-layout {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.hot-table-container {
  flex: 1; /* 左半边占 2/3 宽度 */
}

.hot-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.hot-table th,
.hot-table td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.hot-table th {
  background-color: #c2e8f8;
  font-weight: bold;
  color: #333;
}

.hot-table td {
  color: #666;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 斑马纹样式 */
.hot-table tr:nth-child(odd) {
  background-color: #ffffff; /* 奇数行（白色） */
}

.hot-table tr:nth-child(even) {
  background-color: #dcf2fb; /* 偶数行（浅灰色） */
}

/* 前三名的特殊样式 */
.top-rank {
  color: #e74c3c !important; /* 前三名为红色 */
  font-weight: bold;
}

/* 表格前三行的特殊样式 */
.top-item td {
  color: #e74c3c !important;
  font-weight: bold;
}

.hot-table tr:hover {
  background-color: #e6f7ff; /* 悬停时覆盖斑马纹，保持一致的高亮效果 */
}

.word-cloud-container {
  flex: 1; /* 右半边占 2/3 宽度 */
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8); /* 恢复为白色半透明背景 */
  z-index: 10;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #0056b3;
  font-size: 16px;
  font-weight: bold;
  animation: float 3s infinite ease-in-out;
}

.loading-logo-img {
  width: 100px;
  height: 100px;
  margin-bottom: 15px;
  animation: boat-rock 3s infinite ease-in-out;
  transform-origin: bottom center;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes boat-rock {
  0%,
  100% {
    transform: rotate(-5deg) scale(1.05);
  }
  50% {
    transform: rotate(5deg) scale(1.15);
  }
}

.no-more-data {
  text-align: center;
  padding: 16px;
  color: #999;
  font-size: 14px;
}

/* 响应式设计 - 保持flex动态适应 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column; /* 小屏幕时改为垂直布局 */
  }

  .left-column,
  .right-column {
    flex: none; /* 取消flex比例 */
    width: 100%; /* 全宽显示 */
  }

  .right-column {
    margin-top: 20px;
  }

  /* 小屏幕时为热帖列表设置合理的高度限制 */
  .hot-posts-list {
    max-height: 500px;
    min-height: 400px; /* 与JavaScript中的最小高度保持一致 */
  }
}

@media (max-width: 768px) {
  .main-content {
    gap: 15px;
    margin-bottom: 15px;
  }

  .right-column {
    padding: 15px;
    margin-top: 15px;
  }

  .hot-posts-list {
    max-height: 400px;
    min-height: 350px; /* 移动端稍微减少但仍保证加载动画显示空间 */
  }
}

.title-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recent-posts-tip {
  position: relative;
  background: rgba(255, 243, 224, 0.6);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(255, 152, 0, 0.1);
  box-shadow: 0 2px 6px rgba(255, 152, 0, 0.04);
  animation: fadeIn 0.5s ease-out;
  backdrop-filter: blur(4px);
}

.recent-posts-tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 152, 0, 0.1) 50%, transparent 100%);
}

.tip-content {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.tip-icon {
  font-size: 14px;
  color: rgba(255, 152, 0, 0.6);
  margin-right: 8px;
  animation: gentleFloat 4s ease-in-out infinite;
}

.tip-text {
  font-size: 13px;
  color: rgba(230, 81, 0, 0.7);
  font-weight: normal;
  letter-spacing: 0.2px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* 简化悬停效果 */
.recent-posts-tip:hover {
  background: rgba(255, 243, 224, 0.8);
  transition: background 0.3s ease;
}

.recent-posts-tip:hover .tip-icon {
  color: rgba(255, 152, 0, 0.8);
}
</style>